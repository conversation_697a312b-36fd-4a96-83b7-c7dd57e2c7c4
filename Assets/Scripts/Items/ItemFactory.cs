using System;

namespace Items
{
    public static class ItemFactory
    {
        public static Item CreateItem(int itemID)
        {
            var itemData = ItemDatabase.GetItemByID(itemID);
            return CreateItem(itemData);
        }
        
        public static Item CreateItem(ItemData itemData)
        {
            return CreateItem(itemData, Guid.NewGuid(), 1);
        }

        public static Item CreateItem(ItemData itemData, Guid guid)
        {
            return CreateItem(itemData, guid, 1);
        }

        public static Item CreateItem(ItemData itemData, int quantity)
        {
            return CreateItem(itemData, Guid.NewGuid(), quantity);
        }

        public static Item CreateItem(ItemData itemData, Guid guid, int quantity)
        {
            return itemData switch
            {
                ContainerItemData containerItemData => new ContainerItem(guid, containerItemData),
                StackableItemData stackableItemData => new StackableItem(guid, stackableItemData, quantity),
                _ => new Item(guid, itemData)
            };
        }
    }
}