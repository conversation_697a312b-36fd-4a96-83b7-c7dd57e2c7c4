using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Systems;
using Systems.Services;
using UI;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Items
{
    public class Container : IContainer
    {
        public Action ClearSlotOverlayVisuals;
    
        public string Name { get; }
        public Guid GUID { get; }
        public Vector2Int Dimensions { get; }

        private Dictionary<Vector2Int, Item> Items { get; }
        public List<KeyValuePair<Vector2Int, Item>> SortedList { get; private set; }

        private readonly ItemCategory[] _permittedItemCategories;

        public Vector2 SlotDimensions;

        private ContainerSlot[,] _containerSlots;

        public ContainerSlot[,] ContainerSlots
        {
            get => _containerSlots;
            private set => _containerSlots = value;
        }

        private NotificationSystem _notificationSystem;

        /// <summary>
        /// Creates an empty container
        /// </summary>
        /// <param name="name"> The name of the container </param>
        /// <param name="dimensions"> The dimensions of the container </param>
        /// <param name="slotWidth"> The width of each slot </param>
        /// <param name="slotHeight"> The height of each slot </param>
        /// <param name="permittedItemCategories"> The categories of items that can be placed in the container </param>
        public Container(string name, Vector2Int dimensions, float slotWidth, float slotHeight, ItemCategory[] permittedItemCategories = null)
        {
            Name = name;
            _notificationSystem = ServiceLocator.Locate<NotificationSystem>();
        
            GUID = Guid.NewGuid();
            Dimensions = dimensions;
            SlotDimensions = new Vector2(slotWidth, slotHeight);
            _permittedItemCategories = permittedItemCategories;
            ContainerSlots = new ContainerSlot[Dimensions.x, Dimensions.y];
            SortedList = new List<KeyValuePair<Vector2Int, Item>>();

            Items = new Dictionary<Vector2Int, Item>();

            for (int row = 0; row < Dimensions.x; row++)
            {
                for (int column = 0; column < Dimensions.y; column++)
                {
                    ContainerSlots[row, column] = new ContainerSlot(this, slotWidth, slotHeight);
                }
            }
        }

        /// <summary>
        /// Creates a container with items from the specified loot table
        /// </summary>
        /// <param name="lootTable"> The loot table to use </param>
        /// <param name="dimensions"> The dimensions of the container </param>
        /// <param name="slotWidth"> The width of each slot </param>
        /// <param name="slotHeight"> The height of each slot </param>
        /// <param name="itemCountOverride"> Override the number of items to place in the container with this value </param>
        public Container(LootTable lootTable, Vector2Int dimensions, float slotWidth, float slotHeight, int itemCountOverride = 0) : this(lootTable.name, dimensions,
            slotWidth, slotHeight)
        {
            var numberOfItems = itemCountOverride > 0 ? itemCountOverride : Random.Range(lootTable.MinimumItemCount, lootTable.MaximumItemCount + 1);
        
            for (int i = 0; i < numberOfItems; i++)
            {
                for (int itemPlacementAttempt = 0; itemPlacementAttempt < 3; itemPlacementAttempt++)
                {
                    var itemData = lootTable.GetRandomItem();
                    
                    Item item = itemData is StackableItemData stackableItemData ? ItemFactory.CreateItem(stackableItemData, Random.Range(stackableItemData.DefaultStackMin, stackableItemData.DefaultStackMax)) : ItemFactory.CreateItem(itemData);
                
                    item.ItemDiscarded += OnItemDiscarded;
                    var placedSuccessfully = TryPlaceItem(item, true);
                
                    if (placedSuccessfully)
                    {
                        break;
                    }
                }
            }
        }

        private void OnItemDiscarded(Item item)
        {
            var itemRemoved = TryRemoveItem(item);

            if (itemRemoved)
            {
                _notificationSystem.ShowNotification($"{item.ItemName} discarded from {Name}");
            }
        }

        private void SortContainerList()
        {
            SortedList = Items.OrderBy(kv => kv.Key.x).ThenBy(kv => kv.Key.y).ToList();
        }

        private Vector2Int GetNextAvailableSlot(Item item, bool ignoreStackability = false)
        {
            if (item is null)
            {
                return -Vector2Int.one;
            }
            
            if (!ignoreStackability && item is StackableItem stackableItem)
            {
                var nextMatchingStackable = GetNextMatchingStackable(stackableItem);
                if (nextMatchingStackable != null)
                {
                    return GetItemCoordinates(nextMatchingStackable);
                }
            }
            
            for (int row = 0; row < _containerSlots.GetLength(0); row++)
            {
                for (int column = 0; column < _containerSlots.GetLength(1); column++)
                {
                    if (IsClusterAvailableAt(item, row, column))
                    {
                        return new Vector2Int(row, column);
                    }
                }
            }

            return -Vector2Int.one;
        }

        private StackableItem GetNextMatchingStackable(StackableItem stackableItem)
        {
            return Items.Values.ToList().FirstOrDefault(item => item is StackableItem stackableItemInSlot && stackableItem.CanBeCombinedInto(stackableItemInSlot)) as StackableItem;
        }

        private Vector2Int GetNextAvailableSplitSlot(Item item)
        {
            if (item is null)
            {
                return -Vector2Int.one;
            }
            
            for (int row = 0; row < _containerSlots.GetLength(0); row++)
            {
                for (int column = 0; column < _containerSlots.GetLength(1); column++)
                {
                    if (IsClusterAvailableAt(item, row, column, true))
                    {
                        return new Vector2Int(row, column);
                    }
                }
            }

            return -Vector2Int.one;
        }

        private Vector2Int GetNextAvailableAdjacentSlot(Item originalItem, Item item)
        {
            if (item is null)
            {
                return -Vector2Int.one;
            }
            
            var originalItemCoordinates = GetItemCoordinates(originalItem);
            
            if (originalItemCoordinates.x == -1 || originalItemCoordinates.y == -1)
            {
                return -Vector2Int.one;
            }
            
            var itemSize = item.RotatedSize;
            
            // check right
            if (IsClusterAvailableAt(item, originalItemCoordinates.x, originalItemCoordinates.y + itemSize.x))
            {
                return new Vector2Int(originalItemCoordinates.x, originalItemCoordinates.y + itemSize.x);
            }
            
            // check left
            if (IsClusterAvailableAt(item, originalItemCoordinates.x, originalItemCoordinates.y - itemSize.x))
            {
                return new Vector2Int(originalItemCoordinates.x, originalItemCoordinates.y - itemSize.x);
            }
            
            // check up
            if (IsClusterAvailableAt(item, originalItemCoordinates.x - itemSize.y, originalItemCoordinates.y))
            {
                return new Vector2Int(originalItemCoordinates.x - itemSize.y, originalItemCoordinates.y);
            }
            
            // check down
            if (IsClusterAvailableAt(item, originalItemCoordinates.x + itemSize.y, originalItemCoordinates.y))
            {
                return new Vector2Int(originalItemCoordinates.x + itemSize.y, originalItemCoordinates.y);
            }

            return -Vector2Int.one;
        }

        /// <summary>
        /// Returns the item at the specified coordinates
        /// </summary>
        /// <param name="containerSlotCoordinates"> The coordinates of the item to retrieve </param>
        /// <returns> The item at the specified coordinates </returns>
        public Item GetItemAtPosition(Vector2Int containerSlotCoordinates)
        {
            return Items.GetValueOrDefault(containerSlotCoordinates);
        }

        /// <summary>
        /// Returns the coordinates of the item in the container
        /// </summary>
        /// <param name="itemGuid"> The GUID of the item </param>
        /// <returns> The coordinates of the item </returns>
        public Vector2Int GetItemCoordinates(Guid itemGuid)
        {
            var itemData = Items.FirstOrDefault(pair => pair.Value.GUID == itemGuid).Value;

            if (itemData == null)
            {
                Debug.LogWarning($"ItemData with GUID: {itemGuid} was not found in Container:{GUID}");
                return -Vector2Int.one;
            }

            return GetItemCoordinates(itemData);
        }

        private Vector2Int GetItemCoordinates(Item item)
        {
            if (!Items.ContainsValue(item))
            {
                Debug.LogWarning($"ItemData: {item} not found in Container:{ToString()}");
                return -Vector2Int.one;
            }

            var matchingItems = Items.Where(pair => pair.Value.Equals(item)).ToList();

            Vector2Int topLeftMostCoordinates;

            if (matchingItems.Count > 0)
            {
                topLeftMostCoordinates = matchingItems.Select(pair => pair.Key)
                    .Aggregate((min, current) =>
                        new Vector2Int(Mathf.Min(min.x, current.x), Mathf.Min(min.y, current.y)));
            }
            else
            {
                Debug.Log("No matching items found.");
                topLeftMostCoordinates = -Vector2Int.one;
            }

            return topLeftMostCoordinates;
        }

        /// <summary>
        /// Returns true if the item can be placed in the container at the specified coordinates, taking into account the item's size
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <param name="startingCoordinates"> The coordinates to place the item at </param>
        /// <returns> True if the item can be placed in the container at the specified coordinates </returns>
        private bool IsClusterAvailableAt(Item item, Vector2Int startingCoordinates)
        {
            if (item is StackableItem stackableItem)
            {
                if (GetItemAtPosition(startingCoordinates) is StackableItem stackableItemInSlot)
                {
                    return stackableItem.CanBeCombinedInto(stackableItemInSlot);
                }
            }

            return IsClusterAvailableAt(item, startingCoordinates.x, startingCoordinates.y);
        }

        private bool IsClusterAvailableAt(Item item, int startRow, int startCol, bool ignoreItemGuid = false)
        {
            if (item is null)
            {
                return false;
            }
            
            for (int row = 0; row < item.RotatedSize.y; row++)
            {
                for (int col = 0; col < item.RotatedSize.x; col++)
                {
                    var currentRow = startRow + row;
                    var currentColumn = startCol + col;

                    // if the target coordinates are outside the bounds of the container
                    if (currentRow < 0 || currentColumn < 0 || currentRow >= _containerSlots.GetLength(0) ||
                        currentColumn >= _containerSlots.GetLength(1))
                    {
                        return false;
                    }

                    var containerSlot = _containerSlots[currentRow, currentColumn];
                
                    // If any position in the cluster is false, the cluster is not available
                    if (containerSlot.IsOccupied && (ignoreItemGuid || containerSlot.ItemEncapsulated.GUID != item.GUID))
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Places the item in the container at the specified coordinates
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <param name="coordinates"> The coordinates to place the item at </param>
        public void PlaceItem(Item item, Vector2Int coordinates)
        {
            _containerSlots[coordinates.x, coordinates.y].PlaceItem(item);
            item.ItemDiscarded += OnItemDiscarded;
            Items.Add(new Vector2Int(coordinates.x, coordinates.y), item);
            SortContainerList();
            ClearSlotOverlayVisuals?.Invoke();
        }

        /// <summary>
        /// Places the item in the container at the next available slot
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <returns> True if the item can be placed in the container </returns>
        public bool CanPlaceItem(Item item)
        {
            return CanPlaceItem(item, GetNextAvailableSlot(item));
        }

        /// <summary>
        /// Places the item in the container at the next available slot that isn't the original item's slot
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <returns> True if the item can be placed in the container </returns>
        public bool CanPlaceItemSplit(Item item)
        {
            return CanPlaceItem(item, GetNextAvailableSplitSlot(item));
        }

        /// <summary>
        /// Returns true if the item can be placed in the container at the specified coordinates
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <param name="startingCoordinates"> The coordinates to place the item at </param>
        /// <param name="ignoreStackability"> Ignore Stacking of `StackableItem`s </param>
        /// <returns> True if the item can be placed in the container at the specified coordinates </returns>
        public bool CanPlaceItem(Item item, Vector2Int startingCoordinates, bool ignoreStackability = false)
        {
            if (item is ContainerItem containerItem)
            {
                var placingWithinItself = containerItem.GUID == GUID;

                if (placingWithinItself)
                {
                    _notificationSystem.ShowNotification($"A Container ({Name}) cannot be placed within itself", GUID);
                    return false;
                }
                
                // return false if the container already contains any containers within this hierarchy of containers
                if (containerItem.Container.ContainsContainer(GUID))
                {
                    _notificationSystem.ShowNotification($"A Container ({Name}) cannot be placed within a container that is a child container of itself", GUID);
                    return false;
                }
            }

            if (_permittedItemCategories is {Length: > 0} && !_permittedItemCategories.Contains(item.ItemData.ItemCategory))
            {
                if (!_notificationSystem.HasNotification(item.GUID))
                {
                    // uses ToTitleCase to convert the categories to a more readable format
                    var categoriesString = string.Join(", ", _permittedItemCategories.Select(category => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(category.ToString().ToLower())));
                    _notificationSystem.ShowNotification($"{Name} only accepts {categoriesString}", item.GUID);
                }
                
                return false;
            }
            
            var clusterIsAvailable = IsClusterAvailableAt(item, startingCoordinates);
            if (!clusterIsAvailable)
            {
                return false;
            }
        
            var stackableItemInSlot = GetItemAtPosition(startingCoordinates) as StackableItem;
            if (!ignoreStackability && stackableItemInSlot != null && item is StackableItem stackableItem && !stackableItemInSlot.Equals(stackableItem))
            {
                return stackableItem.CanBeCombinedInto(stackableItemInSlot);
            }

            return true;
        }
        
        /// <summary>
        /// Checks if this container or any of its child containers contains a container with the specified GUID.
        /// Used to prevent circular container references.
        /// </summary>
        /// <param name="containerGuid">The GUID of the container to check for</param>
        /// <returns>True if the container with the specified GUID is found in this container or any of its child containers</returns>
        public bool ContainsContainer(Guid containerGuid)
        {
            // Check if this container is the one we're looking for
            if (GUID == containerGuid)
            {
                return true;
            }
    
            // Check all items in this container
            foreach (var item in Items.Values)
            {
                // If the item is a container, check if it's the one we're looking for or if it contains the one we're looking for
                if (item is ContainerItem containerItem)
                {
                    // Check if this container item is the one we're looking for
                    if (containerItem.GUID == containerGuid)
                    {
                        return true;
                    }
            
                    // Recursively check if this container item's container contains the one we're looking for
                    if (containerItem.Container.ContainsContainer(containerGuid))
                    {
                        return true;
                    }
                }
            }
    
            return false;
        }

        /// <summary>
        /// Attempts to place the item in the next available slot in the container
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <returns> True if the item was placed in the container </returns>
        public bool TryPlaceItem(Item item, bool ignoreStackability = false)
        {
            var nextAvailableSlotCoordinates = GetNextAvailableSlot(item, ignoreStackability);
            
            if (!CanPlaceItem(item, nextAvailableSlotCoordinates, ignoreStackability))
            {
                return false;
            }

            // if it didn't find a place and the item is longer in one direction than another, rotate it and try again
            if (nextAvailableSlotCoordinates == -Vector2Int.one && !item.IsUniformSize)
            {
                item.Rotate();
                nextAvailableSlotCoordinates = GetNextAvailableSlot(item);
            }

            // if it still didn't find a place, return
            if (nextAvailableSlotCoordinates == -Vector2Int.one)
            {
                return false;
            }

            TryPlaceItem(item, nextAvailableSlotCoordinates, ignoreStackability);

            return true;
        }

        /// <summary>
        /// Attempts to place the item in the container
        /// </summary>
        /// <param name="item"> The item to place </param>
        /// <param name="startingCoordinates"> The coordinates to place the item at </param>
        /// <param name="ignoreStackability"> Ignore Stacking of `StackableItem`s </param>
        /// <returns> True if the item was placed in the container </returns>
        public bool TryPlaceItem(Item item, Vector2Int startingCoordinates, bool ignoreStackability = false)
        {
            var canPlaceItem = CanPlaceItem(item, startingCoordinates);

            if (!canPlaceItem)
            {
                // uses ToTitleCase to convert the categories to a more readable format
                var categoriesString = string.Join(", ", _permittedItemCategories.Select(category => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(category.ToString().ToLower())));
                _notificationSystem.ShowNotification($"{Name} only accepts {categoriesString}");
                return false;
            }

            if (!ignoreStackability && item is StackableItem stackableItem
                && GetItemAtPosition(startingCoordinates) is StackableItem stackableItemInSlot
                && stackableItem.CanBeCombinedInto(stackableItemInSlot))
            {
                var itemQuantityFullyTransferred = CombineItems(stackableItemInSlot, stackableItem);

                // recursively try to place the item again if there is any quantity left
                if (!itemQuantityFullyTransferred)
                {
                    Debug.Log($"Partial combination occurred for {stackableItem.ItemName}. Remaining quantity: {stackableItem.StackQuantity}. Attempting overflow placement.");
                    // Use ignoreStackability=true to prevent infinite recursion by avoiding another combination attempt
                    var overflowPlacedSuccessfully = TryPlaceItem(stackableItem, true);
                    Debug.Log($"Overflow placement result: {overflowPlacedSuccessfully}. If false, item should return to original position.");
                    // Return the overflow result - if it fails, the UI will handle returning leftovers to original position
                    return overflowPlacedSuccessfully;
                }

                return true;
            }

            // Only remove the item if it's currently in this container
            // This prevents removing items during drag operations where they might not be in any container
            var itemIsInThisContainer = Items.ContainsValue(item);
            Debug.Log($"Placing {item.ItemName} in {Name}. Item is currently in this container: {itemIsInThisContainer}");
            if (itemIsInThisContainer)
            {
                TryRemoveItem(item);
            }

            PlaceItemInCluster(item, startingCoordinates);

            return true;
        }

        /// <summary>
        /// Attempts to place the item in the container adjacent to the specified coordinates
        /// </summary>
        /// <param name="originalItem"></param>
        /// <param name="item"> The item to place </param>
        /// <returns> True if the item was placed in the container </returns>
        public bool TryPlaceItemAdjacent(Item originalItem, Item item)
        {
            var nextAvailableSlotCoordinates = GetNextAvailableAdjacentSlot(originalItem, item);
            
            if (!CanPlaceItem(item, nextAvailableSlotCoordinates))
            {
                return false;
            }

            return TryPlaceItem(item, nextAvailableSlotCoordinates);
        }

        private void PlaceItemInCluster(Item item, Vector2Int startingCoordinates)
        {
            for (int row = 0; row < item.RotatedSize.y; row++)
            {
                for (int col = 0; col < item.RotatedSize.x; col++)
                {
                    var currentRow = startingCoordinates.x + row;
                    var currentColumn = startingCoordinates.y + col;

                    var coordinateToPlaceItem = new Vector2Int(currentRow, currentColumn);

                    PlaceItem(item, coordinateToPlaceItem);
                }
            }
        }

        /// <summary>
        /// Attempts to remove the item from the container
        /// </summary>
        /// <param name="item"> The item to remove </param>
        /// <returns> True if the item was removed from the container </returns>
        public bool TryRemoveItem(Item item)
        {
            var matchingItemsFromInventory = Items.Where(kvp => kvp.Value.Equals(item)).ToList();

            if (matchingItemsFromInventory.Count <= 0)
            {
                return false;
            }

            foreach (var itemFromInventory in matchingItemsFromInventory)
            {
                RemoveItemAt(itemFromInventory.Key);
            }

            return true;
        }

        private void RemoveItemAt(Vector2Int itemCoordinates)
        {
            Items.Remove(itemCoordinates);
            _containerSlots[itemCoordinates.x, itemCoordinates.y].RemoveItem();
            SortContainerList();
        }

        /// <summary>
        /// Combines the stackableItem into the stackableItemInSlot
        /// </summary>
        /// <param name="stackableItemInSlot"> The item in the slot </param>
        /// <param name="stackableItem"> The item to combine </param>
        /// <returns> True if the stackableItem was fully transferred, false if it should return to its previous position </returns>
        private bool CombineItems(StackableItem stackableItemInSlot, StackableItem stackableItem)
        {
            ClearSlotOverlayVisuals?.Invoke();

            if (stackableItem.CanBeCombinedInto(stackableItemInSlot))
            {
                var successfullyCombined = stackableItem.CombineInto(stackableItemInSlot);

                if (!successfullyCombined)
                {
                    return false;
                }
            }
        
            return stackableItem.StackQuantity <= 0;
        }
    }
}
