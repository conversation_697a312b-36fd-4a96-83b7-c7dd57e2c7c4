using System;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Items
{
    public class StackableItem : Item
    {
        public Action<int> StackQuantityUpdated;
    
        private int _stackQuantity;
        public int StackQuantity
        {
            get => _stackQuantity;
            private set
            {
                _stackQuantity = value;
                StackQuantityUpdated?.Invoke(_stackQuantity);
            }
        }

        public bool StackIsFull => StackQuantity == ItemData.MaxStackSize;

        public new StackableItemData ItemData { get; }
    
        public StackableItem(Guid guid, StackableItemData itemData, int stackQuantity) : base(guid, itemData)
        {
            ItemData = itemData;
            _stackQuantity = stackQuantity;
        }
    
        public StackableItem(StackableItemData itemData, int stackQuantity):this(Guid.NewGuid(), itemData, stackQuantity) { }
    
        public StackableItem(StackableItemData itemData):this(Guid.NewGuid(), itemData, Random.Range(itemData.DefaultStackMin, itemData.DefaultStackMax)) { }

        /// <summary>
        /// Attempts to combine this stack into the `targetStackItem`.
        /// </summary>
        /// <param name="targetStackItem"></param>
        /// <returns>True if this stack was successfully combined into the `targetStackItem`</returns>
        public bool CombineInto(StackableItem targetStackItem)
        {
            if (targetStackItem == null || !CanBeCombinedInto(targetStackItem))
            {
                return false;
            }
    
            // Calculate how many items can actually be transferred.
            var availableSpace = ItemData.MaxStackSize - targetStackItem.StackQuantity;
            var amountToTransfer = Mathf.Min(StackQuantity, availableSpace);
    
            if(amountToTransfer <= 0)
            {
                return false;
            }
    
            // Add the transferred amount to the target.
            targetStackItem.StackQuantity += amountToTransfer;
    
            // Remove the amount from the source stack.
            var stackReductionSuccessful = ReduceStackCount(amountToTransfer);
            
            return stackReductionSuccessful;
        }


        public bool CanBeCombinedInto(StackableItem targetStackItem)
        {
            var itemIDsDontMatch = !MatchesItemID(targetStackItem);
            if (itemIDsDontMatch)
            {
                return false;
            }

            if (targetStackItem.StackIsFull)
            {
                return false;
            }

            if (Equals(targetStackItem))
            {
                return false;
            }
        
            return true;
        }

        private bool ReduceStackCount(int amount)
        {
            if (amount <= 0)
            {
                Debug.Log($"Attempted to reduce stack size of {ItemName}:{GUID}, but was given a negative number ({amount}); resulting in INCREASING stack quantity.");
                return false;
            }
        
            // Subtract the amount.
            StackQuantity -= amount;
    
            if (StackQuantity <= 0)
            {
                StackQuantity = 0;
                LateDiscard();
            }
    
            return true;
        }

        public bool MatchesItemID(StackableItem stackableItem)
        {
            return ItemData.MatchesItemID(stackableItem.ItemData);
        }

        public StackableItem SplitStack()
        {
            if (StackQuantity <= 1)
            {
                return null;
            }
        
            var splitQuantity = StackQuantity / 2;
            ReduceStackCount(splitQuantity);
            
            return new StackableItem(ItemData, splitQuantity);
        }
    }
}