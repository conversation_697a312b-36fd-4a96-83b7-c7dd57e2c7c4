using System;
using System.Collections.Generic;
using Items;
using Systems;
using Systems.Services;
using UI;
using Unity.Mathematics;
using UnityEngine;

namespace DebugConsole
{
    public class DebugController : MonoBehaviour
    {
        const int HELP_BOX_HEIGHT = 100;
        
        public static readonly float PausedTimescale = 0.0f;
        public static float Play_Timescale = 1.0f;
    
        private bool _showConsole;
        private Vector2 _scroll;

        private string _inputString;
        
        private List<string> _logs;
        private List<string> _commandHistory;
        private int _maxHistoryItems = 10;

        #region Systems & Properties

        private DebugInput _debugInput;
        
        private UIManager _uiManager;

        private int _currentHistoryIndex;
        
        private int CurrentHistoryIndex
        {
            get => _currentHistoryIndex;
            set
            {
                _currentHistoryIndex = value;
                _inputString = _commandHistory[_currentHistoryIndex];
            }
        }
        
        private ContainerManager _containerManager;
        
        private ContainerManager ContainerManager
        {
            get
            {
                if (_containerManager == null)
                    _containerManager = ServiceLocator.Locate<ContainerManager>();

                return _containerManager;
            }
        }

        #endregion

        #region Commands

        private static DebugCommand _Help;
        private static DebugCommand _Clear;
        private static DebugCommand<string> _OpenContainer;
        private static DebugCommand<AddItemParams> _AddItem;
        private static DebugCommand<float> _Timescale;

        #endregion

        private List<DebugCommandBase> _commandList;

        private void Awake()
        {
            _debugInput = new DebugInput();
            
            _debugInput.Debug.ToggleConsole.performed += ctx => OnToggleDebug();
            _debugInput.Debug.Escape.performed += ctx => CloseConsole();
            _debugInput.Debug.Traverse.performed += ctx => TraverseHistory((int)ctx.ReadValue<float>());
            _debugInput.Debug.Return.performed += ctx => OnReturn();

            _logs = new List<string>();
            _commandHistory = new List<string>();

            _Help = new DebugCommand("help", "Toggle a list of all available Commands", "help", () =>
            {
                AppendCommandListToLog();
            });

            _Clear = new DebugCommand("clear", "Clears the log display", "clear", () =>
            {
                _logs.Clear();
            });

            _OpenContainer = new DebugCommand<string>("opencontainer", "Opens container via Container GUID", "opencontainer <guid>", OpenContainer);
            
            _AddItem = new DebugCommand<AddItemParams>("additem", "Adds an item to the currently open container or the specified container", "additem <itemID> <quantity> <containerGUID>", AddItem);

            _Timescale = new DebugCommand<float>("timescale", "Sets the Time.timescale to the value provided", "timescale <value>", (timescale) =>
            {
                Play_Timescale = timescale;
            });

            _commandList = new List<DebugCommandBase>
            {
                _Help,
                _Clear,
                _OpenContainer,
                _AddItem,
                _Timescale,
            };
        }

        private void Start()
        {
            _uiManager = ServiceLocator.Locate<UIManager>();
        }

        private void OpenContainer(string containerGuid)
        {
            var guid = Guid.Parse(containerGuid);
            var hasContainer = ContainerManager.HasContainer(guid);
            if (!hasContainer)
            {
                AddStringToConsole($"Container with GUID {containerGuid} not found.");
                return;
            }
            
            ContainerManager.OpenContainer(guid);
        }

        private void AddItem(AddItemParams addItemParams)
        {
            // get the container
            if (_uiManager.FocussedUIWindow is not ContainerWindow windowToAddItemTo)
                return;
            
            if(!string.IsNullOrWhiteSpace(addItemParams.ContainerGUID))
            {
                var guid = Guid.Parse(addItemParams.ContainerGUID);
                var hasContainer = ContainerManager.HasContainer(guid);
                if (!hasContainer)
                {
                    AddStringToConsole($"Container with GUID {addItemParams.ContainerGUID} not found.");
                    return;
                }
                
                windowToAddItemTo = _uiManager.GetContainerWindow(guid);
            }
            
            var placedSuccessfully = windowToAddItemTo.TryAddItem(addItemParams.ItemID, addItemParams.Quantity);
            
            if (!placedSuccessfully)
            {
                AddStringToConsole($"Failed to add {addItemParams.ItemID} to {windowToAddItemTo.ContainerName}");
            }
        }

        #region Event Handlers

        private void OnEnable()
        {
            _debugInput.Debug.Enable();
        }

        private void OnDisable()
        {
            _debugInput.Debug.Disable();
        }

        private void OnGUI()
        {
            if(!_showConsole)
                return;

            var yPosition = 0.0f;

            var screenWidth = Screen.width;
            GUI.Box(new Rect(0, yPosition, screenWidth, HELP_BOX_HEIGHT), "");

            var commandLabelHeight = 20;
            var viewport = new Rect(0, 0, screenWidth - 30, commandLabelHeight * _logs.Count);
            _scroll = GUI.BeginScrollView(new Rect(0, yPosition + 5.0f, screenWidth, 90.0f), _scroll, viewport);

            for (int i = 0; i < _logs.Count; i++)
            {
                var log = _logs[i];
                
                var labelRect = new Rect(5, commandLabelHeight * i, viewport.width - 10, commandLabelHeight);
                
                GUI.Label(labelRect, log);
            }
            
            GUI.EndScrollView();
            
            yPosition += HELP_BOX_HEIGHT;
            
            GUI.Box(new Rect(0, yPosition, screenWidth, 30), "");
            GUI.backgroundColor = new Color(0, 0, 0, 0);
            GUI.SetNextControlName("CommandField");
            _inputString = GUI.TextField(new Rect(10.0f, yPosition + 5.0f, screenWidth - 20.0f, 20.0f), _inputString);
            
            GUI.FocusControl("CommandField");
        }

        private void OnToggleDebug()
        {
            if (_showConsole)
                CloseConsole();
            else
                OpenConsole();
        }

        private void OnReturn()
        {
            if(_showConsole)
            {
                HandleInput();
                _currentHistoryIndex = -1;
                _inputString = string.Empty;
            }
        }

        #endregion

        private void OpenConsole()
        {
            _showConsole = true;
            
            // _debugInput.Player.Disable();
            // Time.timeScale = PausedTimescale;
            //
            // Cursor.visible = true;
            // Cursor.lockState = CursorLockMode.None;
        }
        
        private void CloseConsole()
        {
            _showConsole = false;

            // _debugInput.Player.Enable();
            // Time.timeScale = Play_Timescale;
            //
            // Cursor.visible = false;
            // Cursor.lockState = CursorLockMode.Locked;

            if(!string.IsNullOrWhiteSpace(_inputString))
                _inputString = _inputString.Replace("`", string.Empty);
        }

        private void HandleInput()
        {
            if (_inputString == null)
                return;
            
            _inputString = _inputString.Trim();
            if (string.IsNullOrEmpty(_inputString))
                return;
            
            AppendInputToLog();
            AppendInputToCommandHistory();

            var parameters = _inputString.Split(' ');
            
            bool commandFound = false;
            foreach (DebugCommandBase command in _commandList)
            {
                if (!_inputString.Contains(command.CommandId)) continue;

                switch (command)
                {
                    case DebugCommand debugCommand:
                        debugCommand.Invoke();
                        commandFound = true;
                        break;
                    case DebugCommand<float> floatCommand:
                        floatCommand.Invoke(float.Parse(parameters[1]));
                        commandFound = true;
                        break;
                    case DebugCommand<string> stringCommand:
                        stringCommand.Invoke(parameters[1]);
                        commandFound = true;
                        break;
                    case DebugCommand<AddItemParams> addItemCommand:
                        var addItemParams = new AddItemParams
                        {
                            ItemID = parameters[1],
                            Quantity = parameters.Length > 2 ? int.Parse(parameters[2]) : 1,
                            ContainerGUID = parameters.Length > 3 ? parameters[3] : string.Empty
                        };
                        addItemCommand.Invoke(addItemParams);
                        commandFound = true;
                        break;
                }
                
                if (commandFound)
                    break;
            }
            
            if (!commandFound)
            {
                AddStringToConsole($"Command not found: {_inputString}");
            }
        }

        private void AppendInputToLog()
        {
            AddStringToConsole(_inputString);
        }

        private void AddStringToConsole(string consoleMessage)
        {
            _logs.Add(consoleMessage);

            ScrollDebugConsoleDown();
        }

        private void ScrollDebugConsoleDown(int numberOfLines = 1)
        {
            _scroll = Vector2.up * numberOfLines * (_logs.Count * 20);
        }

        private void AppendCommandListToLog()
        {
            foreach (var command in _commandList)
            {
                AddStringToConsole($"{command.CommandFormat} - {command.CommandDescription}");
            }
        }

        private void AppendInputToCommandHistory()
        {
            if (_commandHistory.Contains(_inputString))
                _commandHistory.Remove(_inputString);

            _commandHistory.Insert(0, _inputString);

            if (_commandHistory.Count > _maxHistoryItems)
                _commandHistory.RemoveAt(_maxHistoryItems);
        }

        private void TraverseHistory(int direction)
        {
            if (_commandHistory.Count <= 0)
                return;

            CurrentHistoryIndex = math.clamp(CurrentHistoryIndex + direction, 0, _commandHistory.Count - 1);
        }
    }

    internal class AddItemParams
    {
        public string ItemID;
        public int Quantity;
        public string ContainerGUID = string.Empty;
    }
}
