using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Items;
using Systems;
using UI.ContextMenu;
using UnityEngine;
using UnityEngine.UIElements;

namespace UI
{
    public class ContainerWindow : UIWindow
    {
        private VisualTreeAsset inventoryWindowAsset;
        private VisualTreeAsset inventorySlotAsset;
        private VisualTreeAsset inventorySlotVisualsAsset;
        private VisualTreeAsset inventorySpacerSlotAsset;

        public Vector2Int Dimensions { get; }
        private float SlotWidth;
        private float SlotHeight;

        private VisualElement[,] _inventoryGridSlotElements;
        private Container Container;
        
        public string ContainerName => Container.Name;

        private VisualElement GridVisuals;
        private VisualElement GridElements;
    
        public Guid ContainerGUID { get; private set; }
    
        public ContainerWindow(VisualElement parent, Container container)
            : base(container.Name, parent)
        {
            if (UIManager == null)
            {
                Debug.LogError("No UIManager found!");
                return;
            }
        
            Dimensions = container.Dimensions;

            inventoryWindowAsset = UIManager.InventoryWindowAsset;
            inventorySlotVisualsAsset = UIManager.InventorySlotVisualsAsset;
            inventorySpacerSlotAsset = UIManager.InventorySpacerSlotAsset;

            SlotWidth = container.SlotDimensions.x;
            SlotHeight = container.SlotDimensions.y;

            var rows = container.Dimensions.x;
            var columns = container.Dimensions.y;
            Container = container;
            ContainerGUID = Container.GUID;
            Container.ClearSlotOverlayVisuals += OnClearSlotOverlayVisualsRequested;
        
            // Calculate the size of the window based on slot dimensions and grid
            var containedWidth = columns * SlotWidth;
            var containedHeight = rows * SlotHeight;
            var totalWidth = containedWidth + 4 + 2; // 4px for slot-group margin & 2px for the grid border
            var totalHeight = containedHeight + 30 + 4 + 2; // 30px for title bar, 4px for slot-group margin & 2px for the grid border

            style.width = totalWidth;
            style.height = totalHeight;

            // Get the grid container from the instantiated content
            if (content == null)
            {
                Debug.LogError("Content not found in the ContainerWindow VisualTreeAsset.");
                return;
            }

            // Set up the content layout for the grids
            GridVisuals = UIManager.InventorySlotGroupAsset.Instantiate().Q("Slot-Group");
            GridVisuals.name = "GridVisuals";
            GridVisuals.style.width = containedWidth;
            GridVisuals.style.height = containedHeight;

            GridElements = UIManager.InventorySlotGroupAsset.Instantiate().Q("Slot-Group");
            GridElements.name = "ItemsGrid";
            GridElements.style.width = containedWidth;
            GridElements.style.height = containedHeight;

            AddToContent(GridVisuals);
            AddToContent(GridElements);

            _inventoryGridSlotElements = new VisualElement[Container.Dimensions.x, Container.Dimensions.y];
            CreateGrid(rows, columns);
        }

        protected override void OnOpened()
        {
            base.OnOpened();

            PopulateGrid();
            UIManager.ItemDragStateChanged += OnItemDragStateChanged;
        }

        protected override void OnClosed()
        {
            base.OnClosed();
        
            UIManager.ItemDragStateChanged -= OnItemDragStateChanged;
        }

        private void OnClearSlotOverlayVisualsRequested()
        {
            SetAllSlotsInactive();
        }

        private void OnItemDragStateChanged(InventoryItemElement itemElement)
        {
            if (itemElement != null)
            {
                var itemElementItem = itemElement.Item as StackableItem;
                SetAllRelevantStackableSlotsStackable(itemElementItem);
            }
            else
            {
                SetAllSlotsInactive();
            }
        }

        private void CreateGrid(int rows, int columns)
        {
            for (int row = 0; row < rows; row++)
            {
                for (int column = 0; column < columns; column++)
                {
                    var slotVisuals = inventorySlotVisualsAsset.Instantiate();
                    slotVisuals.style.width = SlotWidth;
                    slotVisuals.style.height = SlotHeight;
                    slotVisuals.pickingMode = PickingMode.Ignore;

                    // Add the slot to the grid
                    GridVisuals.contentContainer.Add(slotVisuals);

                    // Add the slot to the grid
                    var containerSlot = Container.ContainerSlots[row, column];
                    GridElements.contentContainer.Add(containerSlot.element);
                    containerSlot.element.pickingMode = PickingMode.Ignore;

                    _inventoryGridSlotElements[row, column] = containerSlot.element.contentContainer;
                }
            }
        }

        private async void PopulateGrid()
        {
            await EndOfFrameAwaiter.WaitForEndOfFrameAsync();

            var registeredItems = new List<Guid>();
            
            ClearItemVisuals();
        
            foreach (var containerItem in Container.SortedList)
            {
                var containerSlotCoordinates = containerItem.Key;
                var item = containerItem.Value;
            
                if(registeredItems.Contains(item.GUID))
                {
                    continue;
                }
            
                registeredItems.Add(item.GUID);
            
                var inventoryItemElement = new InventoryItemElement(item, this);
                var placedSuccessfully  = inventoryItemElement.InitialSnapToInventory(this, containerSlotCoordinates);

                if (!placedSuccessfully)
                {
                    Debug.Log($"{item.ItemName} was not able to be placed successfully");
                }
            }
        }

        private void ClearItemVisuals()
        {
            foreach (var inventoryGridSlotElement in _inventoryGridSlotElements)
            {
                if(inventoryGridSlotElement.childCount > 1)
                    inventoryGridSlotElement.RemoveAt(0);
            }
        }

        public Vector2Int GetSlotCoordinatesOfItem(Item item)
        {
            var itemCoordinates = Container.GetItemCoordinates(item.GUID);
            return itemCoordinates;
        }

        public Vector2Int GetSlotCoordinatesAt(Vector2 screenPosition)
        {
            var positionWithinWindow = screenPosition - layout.position - new Vector2(0, titleBar.layout.height);

            var slotXPosition = Mathf.FloorToInt(positionWithinWindow.y / SlotWidth);
            var slotYPosition = Mathf.FloorToInt(positionWithinWindow.x / SlotHeight);
            return new Vector2Int(slotXPosition, slotYPosition);
        }

        public VisualElement GetSlotAtContainerCoordinates(Vector2Int gridCoordinates)
        {
            if (gridCoordinates.x < 0 || gridCoordinates.y < 0 ||
                gridCoordinates.x >= _inventoryGridSlotElements.GetLength(0) ||
                gridCoordinates.y >= _inventoryGridSlotElements.GetLength(1))
            {
                Debug.Log($"Slot {gridCoordinates.x},{gridCoordinates.y} out of range");
                return null;
            }
        
            return _inventoryGridSlotElements[gridCoordinates.x, gridCoordinates.y];
        }

        public VisualElement GetSlotAtScreenPosition(Vector2 screenPosition)
        {
            var gridCoordinates = GetSlotCoordinatesAt(screenPosition);

            if (gridCoordinates.x < 0 || gridCoordinates.y < 0)
                return null;
        
            return _inventoryGridSlotElements[gridCoordinates.x, gridCoordinates.y];
        }

        /// <summary>
        /// Attempts to find a stackable item in the container that can be combined with the `stackableItem`
        /// </summary>
        /// <param name="stackableItem">The item to check for a match</param>
        /// <param name="ignoredItems">List of items that should be ignored when checking for a match</param>
        /// <returns>The next matching stackable item</returns>
        public StackableItem GetNextMatchingStackable(StackableItem stackableItem, List<StackableItem> ignoredItems = null)
        {
            for (int row = 0; row < Dimensions.x; row++)
            {
                for (int col = 0; col < Dimensions.y; col++)
                {
                    var slotCoordinates = new Vector2Int(row, col);
                    
                    var itemAtPosition = Container.GetItemAtPosition(slotCoordinates);

                    if (itemAtPosition == null)
                    {
                        continue;
                    }
                    
                    // skip ignored items
                    if (ignoredItems != null && ignoredItems.Contains(itemAtPosition as StackableItem))
                    {
                        continue;
                    }
                    
                    var stackableItemInSlot = itemAtPosition as StackableItem;

                    if (stackableItemInSlot == null)
                        continue;

                    var canBeCombined = stackableItem.CanBeCombinedInto(stackableItemInSlot);

                    if (canBeCombined)
                    {
                        return stackableItemInSlot;
                    }
                }
            }

            return null;
        }

        public void PropagateHover(Vector2 pointerPosition, Item item)
        {
            var slotCoordinates = GetSlotCoordinatesAt(pointerPosition);

            SetAllSlotsInactive();
            SetSlotsActive(slotCoordinates, item);
        }

        public void PropagateMouseRelease()
        {
            SetAllSlotsInactive();
        }

        public bool CanPlaceItem(Item item)
        {
            return Container.CanPlaceItem(item);
        }

        public bool CanPlaceItem(Item item, Vector2Int startingCoordinates)
        {
            return Container.CanPlaceItem(item, startingCoordinates);
        }

        public bool CanPlaceItemSplit(Item item)
        {
            return Container.CanPlaceItemSplit(item);
        }

        public bool VerifyItemPosition(Item item, Vector2Int targetContainerCoordinates)
        {
            var itemAtCoordinates = Container.GetItemAtPosition(targetContainerCoordinates);

            if (itemAtCoordinates == null)
            {
                Debug.LogError($"No ItemData found at position {targetContainerCoordinates} in Container:{ContainerGUID}");
            }
        
            var itemFoundSuccessfully = itemAtCoordinates.GUID == item.GUID;
            return itemFoundSuccessfully;
        }

        public bool TryPlaceItem(Item item, Vector2Int targetContainerCoordinates)
        {
            var placedSuccessfully = Container.TryPlaceItem(item, targetContainerCoordinates);
            return placedSuccessfully;
        }

        public bool TryPlaceItem(Item item)
        {
            var placedSuccessfully = Container.TryPlaceItem(item);
            return placedSuccessfully;
        }

        public void TryPlaceSplitItem(Item originalItem, Item splitItem)
        {
            var placedSuccessfully = Container.TryPlaceItemAdjacent(originalItem, splitItem);
            
            if (!placedSuccessfully)
            {
                TryPlaceItem(splitItem);
            }
            
            PopulateGrid();
        }

        public void TryRemoveItem(Item item)
        {
            Container.TryRemoveItem(item);
        }

        private void SetSlotsActive(Vector2Int slotCoordinates, Item item)
        {
            var dimensions = item.RotatedSize;
            var clusterIsAvailable = Container.CanPlaceItem(item, slotCoordinates);

            for (int row = 0; row < dimensions.y; row++)
            {
                for (int col = 0; col < dimensions.x; col++)
                {
                    var currentRow = slotCoordinates.x + row;
                    var currentColumn = slotCoordinates.y + col;

                    if (currentRow < 0 || currentColumn < 0 || currentRow >= Container.ContainerSlots.GetLength(0) ||
                        currentColumn >= Container.ContainerSlots.GetLength(1))
                        continue;

                    var itemAtPosition = Container.GetItemAtPosition(slotCoordinates);
                    var isOverStackableItem = itemAtPosition is StackableItem;

                    var containerSlot = Container.ContainerSlots[currentRow, currentColumn];
                    containerSlot.SetOverlayActive(true);
                    containerSlot.AdjustOverlayStyle(clusterIsAvailable, isOverStackableItem);
                }
            }
        }

        public void SetAllSlotsInactive()
        {
            for (int row = 0; row < Container.ContainerSlots.GetLength(0); row++)
            {
                for (int col = 0; col < Container.ContainerSlots.GetLength(1); col++)
                {
                    var coordinateToOfCurrentSlot = new Vector2Int(row, col);

                    var containerSlot = Container.ContainerSlots[coordinateToOfCurrentSlot.x, coordinateToOfCurrentSlot.y];
                    containerSlot.SetOverlayActive(false);
                    containerSlot.RemoveOverlayStyle();
                }
            }       

            if (UIManager.ItemBeingDragged != null)
            {
                var stackableItem = UIManager.ItemBeingDragged.Item as StackableItem;
                SetAllRelevantStackableSlotsStackable(stackableItem);
            }
        }

        private void SetAllRelevantStackableSlotsStackable(StackableItem item)
        {
            for (int row = 0; row < Container.ContainerSlots.GetLength(0); row++)
            {
                for (int col = 0; col < Container.ContainerSlots.GetLength(1); col++)
                {
                    var coordinateToOfCurrentSlot = new Vector2Int(row, col);
                
                    var containerSlot = Container.ContainerSlots[coordinateToOfCurrentSlot.x, coordinateToOfCurrentSlot.y];

                    if (containerSlot == null)
                    {
                        Debug.LogError($"Container Slot at ({coordinateToOfCurrentSlot.x},{coordinateToOfCurrentSlot.y}) of container {Container} was not found.");
                        continue;
                    }
                
                    if (item == null ||
                        containerSlot.ItemEncapsulated is not StackableItem stackableItemInSlot ||
                        stackableItemInSlot.StackIsFull ||
                        item.Equals(stackableItemInSlot)
                       )
                    {
                        continue;
                    }
                
                    if (!item.MatchesItemID(stackableItemInSlot))
                    {
                        continue;
                    }
                
                    containerSlot.SetStackableOverlayVisuals();
                }
            }
        }
        
        public override List<ContextMenuItemData> SetUpContextMenuItems()
        {
            base.SetUpContextMenuItems();
            
            var contextItemPrintContainerGuid = new ContextMenuItemData("Debug/Print Container GUID", PrintContainerGUID);
            ContextMenuItems.Insert(0, contextItemPrintContainerGuid);
            
            var contextItemDiscardAllItems = new ContextMenuItemData("Debug/Discard All Items", DiscardAllItems);
            ContextMenuItems.Add(contextItemDiscardAllItems);
            
            var contextItemRefresh = new ContextMenuItemData("Debug/Refresh", RefreshGrid);
            ContextMenuItems.Add(contextItemRefresh);

            var contextItemTransferAllWindows = new ContextMenuItemData("Transfer All", TransferAllWindowAction);
            ContextMenuItems.Add(contextItemTransferAllWindows);

            var automateStacks = new ContextMenuItemData("Automate Stacks", AutoStackAction);
            ContextMenuItems.Add(automateStacks);

            var organizeContainer = new ContextMenuItemData("Organize", OrganizeContainer);
            ContextMenuItems.Add(organizeContainer);

            return ContextMenuItems;
        }

        private void PrintContainerGUID(DropdownMenuAction obj)
        {
            Debug.Log(ToString());

            ClipboardUtility.CopyToClipboard(Container.GUID.ToString());
        }
        
        private void DiscardAllItems(DropdownMenuAction obj)
        {
            for (int row = 0; row < Dimensions.x; row++)
            {
                for (int col = 0; col < Dimensions.y; col++)
                {
                    var itemElementAtPosition = _inventoryGridSlotElements[row, col].Q<InventoryItemElement>();

                    itemElementAtPosition?.Item.Discard();
                }
            }
        }

        private void RefreshGrid(DropdownMenuAction obj)
        {
            PopulateGrid();
        }

        private void TransferAllWindowAction(DropdownMenuAction obj)
        {
            TransferAllToFocusedWindow();
        }

        private async void TransferAllToFocusedWindow()
        {
            var focussedUIWindow = UIManager.FocussedUIWindow;

            if (focussedUIWindow == this)
            {
                return;
            }

            if (focussedUIWindow is not ContainerWindow)
                return;

            for (int row = 0; row < Dimensions.x; row++)
            {
                for (int col = 0; col < Dimensions.y; col++)
                {
                    var itemElementAtPosition = _inventoryGridSlotElements[row, col].Q<InventoryItemElement>();

                    await EndOfFrameAwaiter.WaitForEndOfFrameAsync();
                    itemElementAtPosition?.QuickTransferItemToActiveWindow();
                }
            }
        }

        private void AutoStackAction(DropdownMenuAction obj)
        {
            AutomateStacking();
        }

        private async Task AutomateStacking()
        {
            for (int row = Dimensions.x - 1; row >= 0; row--)
            {
                for (int col = Dimensions.y - 1; col >= 0; col--)
                {
                    var itemElementAtPosition = _inventoryGridSlotElements[row, col].Q<InventoryItemElement>();

                    if (itemElementAtPosition == null)
                    {
                        continue;
                    }

                    if (itemElementAtPosition.Item is StackableItem {StackIsFull: true})
                    {
                        continue;
                    }

                    await EndOfFrameAwaiter.WaitForEndOfFrameAsync();
                    itemElementAtPosition?.TryTransferToContainerWindow(this);
                }
            }
        }

        private void OrganizeContainer(DropdownMenuAction obj)
        {
            OrganizeContainer();
        }

        private async void OrganizeContainer()
        {
            try
            {
                await AutomateStacking();
            
                Dictionary<Guid, InventoryItemElement> itemsInContainer = new Dictionary<Guid, InventoryItemElement>();

                for (int row = 0; row < Dimensions.x; row++)
                {
                    for (int col = 0; col < Dimensions.y; col++)
                    {
                        var itemElementAtPosition = _inventoryGridSlotElements[row, col].Q<InventoryItemElement>();

                        if (itemElementAtPosition == null)
                        {
                            continue;
                        }

                        var addedSuccessfully = itemsInContainer.TryAdd(itemElementAtPosition.Item.GUID, itemElementAtPosition);
                    
                        if(!addedSuccessfully)
                            continue;
                    
                        TryRemoveItem(itemElementAtPosition.Item);
                        itemElementAtPosition.RemoveFromHierarchy();
                    }
                }

                var organizedItemsInContainer = itemsInContainer.OrderBy(item => item.Value.Item.ItemData.ItemID).ToList();

                foreach (var inventoryItemElement in organizedItemsInContainer)
                {
                    await EndOfFrameAwaiter.WaitForEndOfFrameAsync();
                    TryPlaceItem(inventoryItemElement.Value.Item);
                }

                await EndOfFrameAwaiter.WaitForEndOfFrameAsync();
                PopulateGrid();
            }
            catch (Exception e)
            {
                throw; // TODO handle exception
            }
        }

        public override string ToString()
        {
            return $"{Container.Name}:{ContainerGUID}";
        }

        public bool TryAddItem(string itemID, int quantity)
        {
            bool placedSuccessfully = false;
            
            var itemData = ItemDatabase.GetItemByID(int.Parse(itemID));

            if (itemData is StackableItemData stackableItemData)
            {
                var numberOfStacks = Mathf.CeilToInt(quantity / (float)stackableItemData.MaxStackSize);
                var currentStackQuantity = quantity;
                
                // create a new item for each stack
                for (int i = 0; i < numberOfStacks; i++)
                {
                    var stackQuantity = Mathf.Min(currentStackQuantity, stackableItemData.MaxStackSize);
                    var stackableItem = ItemFactory.CreateItem(itemData, stackQuantity) as StackableItem;
                    placedSuccessfully = TryPlaceItem(stackableItem);
                    
                    currentStackQuantity -= stackQuantity;
                    
                    if (!placedSuccessfully)
                    {
                        if (CombineAddedItemIntoStack(stackableItem))
                            continue;
                        
                        // unload the item
                        stackableItem.Discard();
                        break;
                    }
                }
            }
            else
            {
                for (int i = 0; i < quantity; i++)
                {
                    var item = ItemFactory.CreateItem(itemData);
                    placedSuccessfully = TryPlaceItem(item);
                }
            }
            
            PopulateGrid();
            return placedSuccessfully;
        }

        /// <summary>
        /// Attempts to combine the `stackableItem` into an existing stack in the container.
        /// </summary>
        /// <param name="stackableItem">The item to combine into</param>
        /// <returns>True if the item was fully combined into an existing stack </returns>
        private bool CombineAddedItemIntoStack(StackableItem stackableItem)
        {
            List<StackableItem> stacksChecked = new List<StackableItem>();
            var nextMatchingStackable = GetNextMatchingStackable(stackableItem, stacksChecked);
            var combinedSuccessfully = stackableItem.CombineInto(nextMatchingStackable);
            
            // if the item was not fully combined, try again recursively
            if (!combinedSuccessfully)
            {
                stacksChecked.Add(nextMatchingStackable);
                CombineAddedItemIntoStack(stackableItem);
            }

            return true;
        }
    }
}